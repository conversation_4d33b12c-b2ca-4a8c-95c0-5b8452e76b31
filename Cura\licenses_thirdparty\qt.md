Sources for the Qt modules that <PERSON><PERSON> uses (links within UltiMaker repositories):

- qtbase: https://github.com/Ultimaker/qtbase
- qtdeclarative: https://github.com/Ultimaker/qtdeclarative
- qtsvg: https://github.com/Ultimaker/qtsvg
- qtshadertools: https://github.com/Ultimaker/qtshadertools
- qtimageformats: https://github.com/Ultimaker/qtimageformats

Sources for Qt modules that Cura doesn't use (and thus aren't necessary for building or running any part of Cura), but that may be shipped along the product anyway due to our build-process (links to off-site):

- qtsensors: https://github.com/qt/qtsensors
- qtmultimedia: https://github.com/qt/qtmultimedia
- qtpositioning: https://github.com/qt/qtpositioning
- qtremoteobjects: https://github.com/qt/qtremoteobjects
- qttexttospeech: https://github.com/qt/qtspeech
- qtwebchannel: https://github.com/qt/qtwebchannel
- qtwebsockets: https://github.com/qt/qtwebsockets
- qtserialport: https://github.com/qt/qtserialport
- qt (5) (linux): https://github.com/qt/qt5

Versions of Qt used in Cura (from 5.9.1 onwards):

- 5.9.x: Qt 6.6.0
- 5.10.x: Qt 6.6.0
