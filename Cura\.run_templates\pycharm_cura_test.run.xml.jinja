<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="{{ name }}"  type="tests" factoryName="py.test" nameIsGenerated="true">
    <module name="{{ module_name }}" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="PYTHONUNBUFFERED" value="1" />{% for key, value in env_vars.items() %}
      <env name="{{ key }}" value="{{ value }}" />{% endfor %}
    </envs>
    <option name="SDK_HOME" value="{{ sdk_path }}" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tests" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <option name="_new_keywords" value="&quot;&quot;" />
    <option name="_new_parameters" value="&quot;&quot;" />
    <option name="_new_additionalArguments" value="&quot;&quot;" />
    <option name="_new_target" value="&quot;$PROJECT_DIR$/{{ script_name }}&quot;" />
    <option name="_new_targetType" value="&quot;PATH&quot;" />
    <method v="2" />
  </configuration>
</component>
