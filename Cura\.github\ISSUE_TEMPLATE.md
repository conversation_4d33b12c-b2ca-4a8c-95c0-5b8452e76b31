---
name: Old <PERSON>ug report
about: Create a report to help us fix issues.
title: ''
labels: 'Type: Bug'
assignees: ''

---

<!--
Processing an issue will go much faster when this is filled out, and issues which do not use this template WILL BE REMOVED and no fix will be considered!

Before filing, PLEASE check if the issue already exists (either open or closed) by using the search bar on the issues page. If it does, comment there. Even if it's closed, we can reopen it based on your comment.

Also, please note the application version in the title of the issue. For example: "[3.2.1] Cannot connect to 3rd-party printer". Please do NOT write things like "Request:" or "[BUG]" in the title; this is what labels are for.

Thank you for using Cura!
-->

**Application version**
(The version of the application this issue occurs with.)

**Platform**
(Information about the operating system the issue occurs on. Include at least the operating system and maybe GPU.)

**Printer**
(Which printer was selected in Cura?)

**Reproduction steps**
1. (Something you did.)
2. (Something you did next.)

**Screenshot(s)**
(Image showing the problem, perhaps before/after images.) 

**Actual results**
(What happens after the above steps have been followed.)

**Expected results**
(What should happen after the above steps have been followed.)

**Project file**
(For slicing bugs, provide a project which clearly shows the bug, by going to File->Save. For big files you may need to use WeTransfer or similar file sharing sites.)

**Log file**
(See https://github.com/Ultimaker/Cura#logging-issues to find the log file to upload, or copy a relevant snippet from it.)

**Additional information**
(Extra information relevant to the issue.)
