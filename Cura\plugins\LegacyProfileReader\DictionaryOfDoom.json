{"source_version": "15.04", "target_version": "4.5", "translation": {"machine_nozzle_size": "nozzle_size", "line_width": "wall_thickness if (spiralize == \"True\" or simple_mode == \"True\") else (nozzle_size if (float(wall_thickness) < 0.01) else (wall_thickness if (float(wall_thickness) < float(nozzle_size)) else (nozzle_size if ((int(float(wall_thickness) / (float(nozzle_size) - 0.0001))) == 0) else ((float(wall_thickness) / ((int(float(wall_thickness) / (float(nozzle_size) - 0.0001))) + 1)) if ((float(wall_thickness) / (int(float(wall_thickness) / (float(nozzle_size) - 0.0001)))) > float(nozzle_size) * 1.5) else ((float(wall_thickness) / (int(float(wall_thickness) / (float(nozzle_size) - 0.0001)))))))))", "layer_height": "layer_height", "layer_height_0": "bottom_thickness", "wall_thickness": "wall_thickness", "top_thickness": "0 if (solid_top == \"False\") else solid_layer_thickness", "bottom_thickness": "0 if (solid_bottom == \"False\") else solid_layer_thickness", "infill_sparse_density": "fill_density", "infill_overlap": "fill_overlap", "infill_before_walls": "False if (perimeter_before_infill == \"True\") else True", "material_print_temperature": "print_temperature", "material_bed_temperature": "print_bed_temperature", "material_diameter": "filament_diameter", "material_flow": "filament_flow", "retraction_enable": "retraction_enable", "retraction_amount": "retraction_amount", "retraction_speed": "retraction_speed", "retraction_min_travel": "retraction_min_travel", "retraction_hop_enabled": "retraction_hop != 0", "speed_print": "print_speed", "speed_infill": "infill_speed if (float(infill_speed) != 0) else print_speed", "speed_wall_0": "inset0_speed if (float(inset0_speed) != 0) else print_speed", "speed_wall_x": "insetx_speed if (float(insetx_speed) != 0) else print_speed", "speed_topbottom": "solidarea_speed if (float(solidarea_speed) != 0) else print_speed", "speed_travel": "travel_speed if (float(travel_speed) != 0) else travel_speed", "speed_layer_0": "bottom_layer_speed", "retraction_combing": "\"all\" if retraction_combing == \"All\" else \"noskin\" if retraction_combing == \"No Skin\" else \"off\"", "cool_fan_enabled": "fan_enabled", "cool_fan_speed_min": "fan_speed", "cool_fan_speed_max": "fan_speed_max", "cool_fan_full_at_height": "fan_full_height", "cool_min_layer_time": "cool_min_layer_time", "cool_min_speed": "cool_min_feedrate", "cool_lift_head": "cool_head_lift", "support_enable": "False if (support == \"None\") else True", "support_type": "\"buildplate\" if (support == \"Touching buildplate\") else \"everywhere\"", "support_angle": "support_angle", "support_xy_distance": "support_xy_distance", "support_z_distance": "support_z_distance", "support_pattern": "support_type.lower()", "support_infill_rate": "support_fill_rate", "adhesion_type": "\"skirt\" if (platform_adhesion == \"None\") else platform_adhesion.lower()", "skirt_line_count": "skirt_line_count", "skirt_gap": "skirt_gap", "skirt_brim_minimal_length": "skirt_minimal_length", "brim_line_count": "brim_line_count", "raft_margin": "raft_margin", "raft_airgap": "float(raft_airgap_all) + float(raft_airgap)", "layer_0_z_overlap": "raft_airgap", "raft_surface_layers": "raft_surface_layers", "raft_surface_thickness": "raft_surface_thickness", "raft_surface_line_width": "raft_surface_linewidth", "raft_surface_line_spacing": "raft_line_spacing", "raft_interface_thickness": "raft_interface_thickness", "raft_interface_line_width": "raft_interface_linewidth", "raft_interface_line_spacing": "raft_line_spacing", "raft_base_thickness": "raft_base_thickness", "raft_base_line_width": "raft_base_linewidth", "raft_base_line_spacing": "raft_line_spacing", "meshfix_union_all": "fix_horrible_union_all_type_a", "meshfix_union_all_remove_holes": "fix_horrible_union_all_type_b", "meshfix_extensive_stitching": "fix_horrible_extensive_stitching", "meshfix_keep_open_polygons": "fix_horrible_use_open_bits", "magic_mesh_surface_mode": "\"surface\" if simple_mode else \"normal\"", "magic_spiralize": "spiralize", "prime_tower_enable": "wipe_tower", "prime_tower_size": "math.sqrt(float(wipe_tower_volume) / float(layer_height))", "ooze_shield_enabled": "ooze_shield", "skin_overlap": "fill_overlap"}, "defaults": {"bottom_layer_speed": "20", "bottom_thickness": "0.3", "brim_line_count": "20", "cool_head_lift": "False", "cool_min_feedrate": "10", "cool_min_layer_time": "5", "fan_enabled": "True", "fan_full_height": "0.5", "fan_speed": "100", "fan_speed_max": "100", "filament_diameter": "2.85", "filament_diameter2": "0", "filament_diameter3": "0", "filament_diameter4": "0", "filament_diameter5": "0", "filament_flow": "100.0", "fill_density": "20", "fill_overlap": "15", "fix_horrible_extensive_stitching": "False", "fix_horrible_union_all_type_a": "True", "fix_horrible_union_all_type_b": "False", "fix_horrible_use_open_bits": "False", "infill_speed": "0.0", "inset0_speed": "0.0", "insetx_speed": "0.0", "layer_height": "0.1", "layer0_width_factor": "100", "nozzle_size": "0.4", "object_sink": "0.0", "ooze_shield": "False", "overlap_dual": "0.15", "perimeter_before_infill": "False", "platform_adhesion": "None", "print_bed_temperature": "70", "print_speed": "50", "print_temperature": "210", "print_temperature2": "0", "print_temperature3": "0", "print_temperature4": "0", "print_temperature5": "0", "raft_airgap": "0.22", "raft_airgap_all": "0.0", "raft_base_linewidth": "1.0", "raft_base_thickness": "0.3", "raft_interface_linewidth": "0.4", "raft_interface_thickness": "0.27", "raft_line_spacing": "3.0", "raft_margin": "5.0", "raft_surface_layers": "2", "raft_surface_linewidth": "0.4", "raft_surface_thickness": "0.27", "retraction_amount": "4.5", "retraction_combing": "All", "retraction_dual_amount": "16.5", "retraction_enable": "True", "retraction_hop": "0.0", "retraction_min_travel": "1.5", "retraction_minimal_extrusion": "0.02", "retraction_speed": "40.0", "simple_mode": "False", "skirt_gap": "3.0", "skirt_line_count": "1", "skirt_minimal_length": "150.0", "solid_bottom": "True", "solid_layer_thickness": "0.6", "solid_top": "True", "solidarea_speed": "0.0", "spiralize": "False", "support": "None", "support_angle": "60", "support_dual_extrusion": "Both", "support_fill_rate": "15", "support_type": "Lines", "support_xy_distance": "0.7", "support_z_distance": "0.15", "travel_speed": "150.0", "wall_thickness": "0.8", "wipe_tower": "False", "wipe_tower_volume": "15"}}