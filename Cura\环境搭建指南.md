1. 克隆项目
```bash
git clone https://github.com/Ultimaker/Cura.git
git clone https://github.com/Ultimaker/Uranium.git
git clone https://github.com/Ultimaker/CuraEngine.git
```
2. 创建虚拟环境
```bash
python -m venv cura_venv
# On Linux/MacOS
source cura_venv/bin/activate
# On Windows
Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force
.\cura_venv\Scripts\activate.ps1
```
3. 安装conan并下载conan-config
```bash
pip install conan==2.7.0
conan config install https://github.com/wsd07/conan-config.git
conan profile detect --force
```
4. 设置CuraEngine和Uranium为可编辑模式
```bash
# In CuraEngine
cd CuraEngine
conan editable add . --name=curaengine --version=5.11.0 --user=wsd07 --channel=testing
# In Uranium
cd Uranium
conan editable add . --name=uranium --version=5.11.0 --user=wsd07 --channel=testing
```
5. 安装依赖

**重要：首先需要安装Windows SDK**
1. 打开Visual Studio Installer (运行: `C:\Program Files (x86)\Microsoft Visual Studio\Installer\vs_installer.exe`)
2. 点击Visual Studio 2022 Community的"修改"按钮
3. 在"单个组件"选项卡中，勾选：
   - Windows 10 SDK (10.0.22621.0 或最新版本)
   - MSVC v143 - VS 2022 C++ x64/x86 build tools
   - CMake tools for Visual Studio
4. 点击"修改"完成安装

**安装完Windows SDK后，执行以下命令：**
```bash
pip install gitpython
cd Cura

# 方法1：使用开发者命令提示符（推荐）
# 打开新的cmd窗口，运行：
# "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
# 然后激活虚拟环境：cd ..\cura_venv\Scripts && activate.bat && cd ..\..\Cura
# 最后运行conan安装：
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv

# 方法2：在PowerShell中手动设置环境变量
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
$env:PATH += ";C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin"
$env:PATH += ";C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja"
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv -c tools.microsoft.msbuild:installation_path="C:\Program Files\Microsoft Visual Studio\2022\Community" -c tools.cmake.cmaketoolchain:generator="Visual Studio 17 2022"
```
6. 运行项目
```bash
python cura_app.py
```