name: 🪲 Bug Report
description: Create a report to help us fix issues.
labels: ["Type: Bug", "Status: Triage"]
body:
- type: markdown
  attributes:
    value: |
      **Thank you for using Cura and wanting to report a bug. 🙏**
      
      Before filing, [please check if the issue already exists](https://github.com/Ultimaker/Cura/issues?q=is%3Aissue) by using the search bar on the issues page. 
      If it does, comment there. Even if it's closed, we can reopen it based on your comment. 
      
      Please include the cura version in the title of the issue. For example, *"[5.4.0] Support Brim is missing in this model"*. 
- type: input
  attributes:
    label: Cura Version
    description: The version of Cura this issue occurs with.
    placeholder: 5.4.0
  validations:
    required: true
- type: input
  attributes:
    label: Operating System
    description: Information about the operating system the issue occurs on. Include at least the operating system and maybe GPU.
    placeholder:  Windows 11 / MacOS Catalina / MX Linux 
  validations:
    required: true
- type: input
  attributes:
    label: Printer
    description: Which printer was selected in Cura? It also helps to mention if you made any firmware modifications to your printer. 
    placeholder: Ultimaker S7 / Creality CR-10 with <PERSON><PERSON>per     
  validations:
    required: true
- type: textarea
  attributes:
    label: Reproduction steps
    description: Share what you did, so we can reproduce it
    placeholder: |
      1. Something you did
      2. Something you did next
  validations:
    required: true
- type: textarea
  attributes:
    label: Actual results
    description: What happens after the above steps have been followed?
  validations:
    required: true
- type: textarea
  attributes:
    label: Expected results
    description: What should happen after the above steps have been followed?
  validations:
    required: true
- type: markdown
  attributes:
    value: |
      ### Please add the following files when they are related to...
        * 🔵 **The quality of your print**   
          Please add **a Project File**. It contains the printer and settings we need for troubleshooting.    
          To save a project file go to File -> Save project. 
          Please make sure to .zip your project file. For big files, you may need to use [WeTransfer](https://wetransfer.com/) or similar file-sharing sites. 
          G-code files are not project files! Before you share, please think to yourself. Is this a model that can be shared?
          ![Alt Text](https://user-images.githubusercontent.com/40423138/240616958-5a9751f2-bd34-4808-9752-6fde2e27516e.gif)
       * 🔵 **Using and interacting with Cura** 
           Please add **screenshots** showing the issue. 
           Before and after, and arrows can help here. 
       * 🔵 **Unexpected crashes and behavior**
           Please add **a log file** with information on what your Cura is doing.  
           You can find your log file here:
           Windows: `%APPDATA%\cura\<Cura version>\cura.log`
           MacOS: `$USER/Library/Application Support/cura/<Cura version>/cura.log`
           Ubuntu/Linux: `$USER/.local/share/cura/<Cura version>/cura.log` 
           If the Cura user interface still starts, you can also reach this directory from the application menu in Help -> Show settings folder 

- type: textarea
  attributes:
    label: Add your .zip and screenshots here ⬇️ 
    description: You can add the zip file and additional information that is relevant to the issue in the comments below.
  validations:
    required: true
