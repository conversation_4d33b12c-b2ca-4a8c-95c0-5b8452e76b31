name: ❌ Slicing Failed
description: When you see the message Slicing failed with an unexpected error
labels: ["Type: Bug", "Status: Triage", "Slicing Error :collision:"]
body:
- type: markdown
  attributes:
    value: |      
       ### ✨Are you stuck? Have you tried these two things? ✨
       1- Are you on a Cura version lower than Cura 5.7? We really recommend updating because it resolves a lot of slicing crashes!
       2- Have you tried fixing the model with software that repairs 3d files and makes them watertight? 
          Are you seeing spots and dots on your model? That is Cura indicating that your model is not watertight.  
          You can try doing a quick [Mesh Fix with the Meshtools Plugin](https://marketplace.ultimaker.com/app/cura/plugins/fieldofview/MeshTools) or other mesh editing software. 
       
       If you still encounter a crash you are welcome to report the issue so we can use your model as a test case. 
       You can find instructions on how to share your model in a Package for Technical Support below.        
       
       🤔 Before you share, please think to yourself. Is this a model that can be shared on the internet?
       **Unfortunately, we cannot help if this file is missing.** 
     
### Questions
- type: input
  attributes:
    label: Cura Version
    description: We work hard on improving our slicing crashes. If you are not on the latest version of Cura, [you can download it here](https://github.com/Ultimaker/Cura/releases/latest)
  validations:
    required: true
- type: input
  attributes:
    label: Operating System
    description: Information about the operating system the issue occurs on. Include at least the operating system and maybe GPU.
    placeholder: Windows 11 / MacOS Catalina / MX Linux 
  validations:
    required: true
- type: input
  attributes:
    label: Printer
    description: Which printer was selected in Cura?
  validations:
    required: true

- type: textarea
  attributes:
    label: Describe your problem and add the package for technical support as a .zip here ⬇️ 
    description: |
      If you still have Cura open with your crash > Click on Help on top bar > Click on Export Package For Technical Support > Compress the file into a zip > Add the file here to your GitHub issue 🔗
      
      If you closed Cura, please open Cura to recreate the crash> Select your printer > Load your model > Select your print settings > Click on Help on top bar > Click on Export Package For Technical Support > Compress the file into a zip > Add the file here to your GitHub issue 🔗
  validations:
    required: true
