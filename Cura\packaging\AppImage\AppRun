#!/bin/bash

scriptdir=$(dirname $0)

export PYTHONPATH="$scriptdir/lib/python3.10"
export LD_LIBRARY_PATH=$scriptdir
export QT_PLUGIN_PATH="$scriptdir/qt/plugins"
export QML2_IMPORT_PATH="$scriptdir/qt/qml"
export QT_QPA_FONTDIR=/usr/share/fonts
export QT_QPA_PLATFORMTHEME=xdgdesktopportal
export QT_XKB_CONFIG_ROOT=/usr/share/X11/xkb

# Use the openssl.cnf packaged in the AppImage
export OPENSSL_CONF="$scriptdir/openssl.cnf"

# If this variable is set on Zorin OS 16 Cura would crash
# unset `QT_STYLE_OVERRIDE` as a precaution
unset QT_STYLE_OVERRIDE

BIN=`basename "$ARGV0" .AppImage`
if [ -f $scriptdir/$BIN ]; then
    $scriptdir/$BIN "$@"
else
    $scriptdir/UltiMaker-Cura "$@"
fi;
