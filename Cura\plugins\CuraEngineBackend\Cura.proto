syntax = "proto3";

package cura.proto;

message ObjectList
{
    repeated Object objects = 1;
    repeated Setting settings = 2; // meshgroup settings (for one-at-a-time printing)
}

enum SlotID {
  SETTINGS_BROADCAST = 0;
  SIMPLIFY_MODIFY = 100;
  POSTPROCESS_MODIFY = 101;
  INFILL_MODIFY = 102;
  GCODE_PATHS_MODIFY = 103;
  INFILL_GENERATE = 200;
}

message EnginePlugin
{
    SlotID id = 1;
    string address = 2;
    uint32 port = 3;
    string plugin_name = 4;
    string plugin_version = 5;
}

message Slice
{
    repeated ObjectList object_lists = 1; // The meshgroups to be printed one after another
    SettingList global_settings = 2; // The global settings used for the whole print job
    repeated Extruder extruders = 3; // The settings sent to each extruder object
    repeated SettingExtruder limit_to_extruder = 4; // From which stack the setting would inherit if not defined per object
    repeated EnginePlugin engine_plugins = 5;
    string sentry_id = 6; // The anonymized Sentry user id that requested the slice
    string cura_version = 7; // The version of Cura that requested the slice
    optional string project_name = 8; // The name of the project that requested the slice
    optional string user_name = 9; // The Digital Factory account name of the user that requested the slice
}

message Extruder
{
    int32 id = 1;
    SettingList settings = 2;
}

message Object
{
    int64 id = 1;
    bytes vertices = 2; //An array of 3 floats.
    bytes normals = 3; //An array of 3 floats.
    bytes indices = 4; //An array of ints.
    repeated Setting settings = 5; // Setting override per object, overruling the global settings.
    string name = 6; //Mesh name
}

message Progress
{
    float amount = 1;
}

message Layer {
    int32 id = 1;
    float height = 2; // Z position
    float thickness = 3; // height of a single layer

    repeated Polygon polygons = 4; // layer data
}

message Polygon {
    enum Type {
        NoneType = 0;
        Inset0Type = 1;
        InsetXType = 2;
        SkinType = 3;
        SupportType = 4;
        SkirtType = 5;
        InfillType = 6;
        SupportInfillType = 7;
        MoveUnretracted = 8;
        MoveRetracted = 9;
        SupportInterfaceType = 10;
        PrimeTowerType = 11;
        MoveWhileRetracting = 12;
        MoveWhileUnretracting = 13;
        StationaryRetractUnretract = 14;
        NumPrintFeatureTypes = 15;
    }
    Type type = 1; // Type of move
    bytes points = 2; // The points of the polygon, or two points if only a line segment (Currently only line segments are used)
    float line_width = 3; // The width of the line being laid down
    float line_thickness = 4; // The thickness of the line being laid down
    float line_feedrate = 5; // The feedrate of the line being laid down
}

message LayerOptimized {
    int32 id = 1;
    float height = 2; // Z position
    float thickness = 3; // height of a single layer

    repeated PathSegment path_segment = 4; // layer data
}


message PathSegment {
    int32 extruder = 1; // The extruder used for this path segment
    enum PointType {
        Point2D = 0;
        Point3D = 1;
    }
    PointType point_type = 2;
    bytes points = 3; // The points defining the line segments, bytes of float[2/3] array of length N+1
    bytes line_type = 4; // Type of line segment as an unsigned char array of length 1 or N, where N is the number of line segments in this path
    bytes line_width = 5; // The widths of the line segments as bytes of a float array of length 1 or N
    bytes line_thickness = 6; // The thickness of the line segments as bytes of a float array of length 1 or N
    bytes line_feedrate = 7; // The feedrate of the line segments as bytes of a float array of length 1 or N
}


message GCodeLayer {
    bytes data = 2;
}


message PrintTimeMaterialEstimates { // The print time for each feature and material estimates for the extruder
    // Time estimate in each feature
    float time_none = 1;
    float time_inset_0 = 2;
    float time_inset_x = 3;
    float time_skin = 4;
    float time_support = 5;
    float time_skirt = 6;
    float time_infill = 7;
    float time_support_infill = 8;
    float time_travel = 9;
    float time_retract = 10;
    float time_support_interface = 11;
    float time_prime_tower = 12;

    repeated MaterialEstimates materialEstimates = 13; // materialEstimates data
}

message MaterialEstimates {
    int64 id = 1;
    float material_amount = 2; // material used in the extruder
}

message SettingList {
    repeated Setting settings = 1;
}

message Setting {
    string name = 1; // Internal key to signify a setting

    bytes value = 2; // The value of the setting
}

message SettingExtruder {
    string name = 1; //The setting key.

    int32 extruder = 2; //From which extruder stack the setting should inherit.
}

message GCodePrefix {
    bytes data = 2; //Header string to be prepended before the rest of the g-code sent from the engine.
}

message SliceUUID {
    string slice_uuid = 1; //The UUID of the slice.
}

message SlicingFinished {
}
