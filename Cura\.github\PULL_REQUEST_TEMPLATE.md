# Description

<!-- Please include a summary of which issue is fixed or feature was added. Please also include relevant motivation and context. 
If this pull request adds settings definitions for machines/materials, list them here. 

This fixes... OR This improves... -->

## Type of change

<!-- Please delete options that are not relevant. -->

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Printer definition file(s)
- [ ] Translations

# How Has This Been Tested?

<!-- Please describe the tests that you ran to verify your changes. Provide instructions so we can reproduce. Please also list any relevant details for your test configuration -->

- [ ] Test A
- [ ] Test B

**Test Configuration**:
* Operating System:

# Checklist:
<!-- Check if relevant -->

- [ ] My code follows the style guidelines of this project as described in [UltiMaker Meta](https://github.com/Ultimaker/Meta) and [Cura QML best practices](https://github.com/Ultimaker/Cura/wiki/QML-Best-Practices)
- [ ] I have read the [Contribution guide](https://github.com/Ultimaker/Cura/blob/main/CONTRIBUTING.md) 
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have uploaded any files required to test this change
